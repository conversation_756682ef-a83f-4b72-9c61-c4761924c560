<?php

namespace App\Filament\Admin\Resources\CompanyTransactionResource\Pages;

use App\Filament\Admin\Resources\CompanyTransactionResource;
use App\Filament\Admin\Resources\CompanyTransactionResource\Widgets\Widget;
use Filament\Actions;
use Filament\Forms\Form;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\HtmlString;

class ViewCompanyTransaction extends ViewRecord
{
    protected static string $resource = CompanyTransactionResource::class;
    protected static ?string $title = 'Internal Transaction Management';
	public function getHeading(): string|Htmlable
	{
		return new HtmlString($this->record->name);
	}

	public function getSubheading(): string|Htmlable
	{
		return new HtmlString('Account Transaction History');
	}
    protected function getHeaderActions(): array
    {
        return [
            // Actions\EditAction::make(),
        ];
    }

	public function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

	protected function getHeaderWidgets(): array
	{
		return [
			Widget::make(),
		];
	}
}
