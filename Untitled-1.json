2025-07-30 20:43:57.132 [error] [{"type":"methodCall","methodName":"@props","className":null,"arguments":{"type":"arguments","autocompletingIndex":1,"children":[{"type":"argument","name":null,"children":[{"type":"array","children":[{"type":"array_item","key":null,"value":{"type":"string","value":"invoice","start":{"line":0,"column":8},"end":{"line":0,"column":15}}}]}]}]}},{"type":"binary"},{"type":"methodCall","methodName":"@if","className":null,"arguments":{"type":"arguments","autocompletingIndex":1,"children":[{"type":"argument","name":null,"children":[{"type":"methodCall","methodName":"order","className":null,"arguments":{"type":"arguments","autocompletingIndex":0,"children":[]},"children":[]}]}]}},{"type":"binary"},{"type":"binary"},{"type":"methodCall","methodName":"id","className":null,"arguments":{"type":"arguments","autocompletingIndex":0,"children":[]}},{"type":"methodCall","methodName":"order_no","className":null,"arguments":{"type":"arguments","autocompletingIndex":0,"children":[]}},{"type":"methodCall","autocompleting":true,"methodName":"@foreach","className":null,"arguments":{"type":"arguments","autocompletingIndex":1,"children":[{"type":"argument","name":null,"children":[]}]}},{"type":"string","value":"bg-white dark:bg-transparent border-orange-600 text-orange-900 dark:text-orange-100 shadow-md","start":{"line":113,"column":13},"end":{"line":113,"column":106}},{"type":"binary"},{"type":"binary"},{"type":"binary"},{"type":"methodCall","methodName":"id","className":null,"arguments":{"type":"arguments","autocompletingIndex":0,"children":[]}},{"type":"methodCall","methodName":"status","className":null,"arguments":{"type":"arguments","autocompletingIndex":0,"children":[]}},{"type":"methodCall","methodName":"@if","className":null,"arguments":{"type":"arguments","autocompletingIndex":1,"children":[{"type":"argument","name":null,"children":[{"type":"methodCall","methodName":"client","className":null,"arguments":{"type":"arguments","autocompletingIndex":0,"children":[]},"children":[]}]}]}},{"type":"methodCall","methodName":"name","className":null,"arguments":{"type":"arguments","autocompletingIndex":0,"children":[]}},{"type":"methodCall","methodName":"@if","className":null,"arguments":{"type":"arguments","autocompletingIndex":1,"children":[{"type":"argument","name":null,"children":[{"type":"binary","children":[{"type":"binary","children":[{"type":"methodCall","methodName":"count","className":null,"arguments":{"type":"arguments","autocompletingIndex":1,"children":[{"type":"argument","name":null,"children":[]}]},"children":[]}]}]}]}]}},{"type":"methodCall","methodName":"@if","className":null,"arguments":{"type":"arguments","autocompletingIndex":1,"children":[{"type":"argument","name":null,"children":[]}]}},{"type":"methodCall","methodName":"limit","className":"Str","arguments":{"type":"arguments","autocompletingIndex":2,"children":[{"type":"argument","name":null,"children":[{"type":"methodCall","methodName":"name","className":null,"arguments":{"type":"arguments","autocompletingIndex":0,"children":[]},"children":[]}]},{"type":"argument","name":null,"children":[]}]}},{"type":"binary"},{"type":"binary"},{"type":"binary"},{"type":"methodCall","methodName":"number_format","className":null,"arguments":{"type":"arguments","autocompletingIndex":2,"children":[{"type":"argument","name":null,"children":[{"type":"binary","children":[{"type":"methodCall","methodName":"invoice_amount","className":null,"arguments":{"type":"arguments","autocompletingIndex":0,"children":[]},"children":[]}]}]},{"type":"argument","name":null,"children":[]}]}},{"type":"methodCall","methodName":"@if","className":null,"arguments":{"type":"arguments","autocompletingIndex":1,"children":[{"type":"argument","name":null,"children":[{"type":"methodCall","methodName":"parent_invoice_id","className":null,"arguments":{"type":"arguments","autocompletingIndex":0,"children":[]},"children":[]}]}]}},{"type":"methodCall","methodName":"@if","className":null,"arguments":{"type":"arguments","autocompletingIndex":1,"children":[{"type":"argument","name":null,"children":[{"type":"methodCall","methodName":"exists","className":null,"arguments":{"type":"arguments","autocompletingIndex":0,"children":[]},"children":[{"type":"methodCall","methodName":"childInvoice","className":null,"arguments":{"type":"arguments","autocompletingIndex":0,"children":[]},"children":[]}]}]}]}},{"type":"methodCall","methodName":"@if","className":null,"arguments":{"type":"arguments","autocompletingIndex":1,"children":[{"type":"argument","name":null,"children":[{"type":"binary","children":[]}]}]}}]
