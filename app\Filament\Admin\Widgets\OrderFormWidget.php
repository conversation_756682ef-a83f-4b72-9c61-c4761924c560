<?php

namespace App\Filament\Admin\Widgets;

use App\Models\Company;
use App\Models\CompanyBank;
use App\Models\CompanyDepositSummary;
use App\Models\Currency;
use App\Models\Invoice;
use App\Models\McAgent;
use App\Models\Order;
use Carbon\Carbon;
use Filament\Forms\Components\{DatePicker, Fieldset, Group, Hidden, Placeholder, Select, TextInput};
use Filament\Forms\Components\Actions\Action as ActionsAction;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Widgets\Widget;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\HtmlString;

class OrderFormWidget extends Widget implements HasForms
{
	use InteractsWithForms;

	protected static string $view = 'filament.admin.widgets.order-form-widget';
	protected static ?string $heading = 'New Order';

	protected int | string | array $columnSpan = 3;
	public static function canView(): bool
    {
        return Auth::user()->hasAnyRole(['admin', 'Super Admin', 'Staff Order']);
    }
	public static function canAccess(): bool
    {
        return Auth::user()->hasAnyRole(['admin', 'Super Admin', 'Staff Order']);
    }

	public ?array $data = [];

	public function mount(): void
	{
		$this->form->fill();
	}

	public function form(Form $form): Form
	{
		return $form
			->schema([
				Hidden::make('order_create_by')
					->default(Auth::user()->id),
				Hidden::make('total'),
				Hidden::make('status')->default('Draft'),
				Hidden::make('balance'),

				Group::make()
					->columns(2)
					->columnSpanFull()
					->schema([
						Select::make('agent_id')
							->label('Select Agent')
							->searchable()
							->required()
							->reactive()
							->columnSpan([
								'default' => 8,
								'lg' => 4,
							])
							->options(fn() => McAgent::pluck('name', 'id'))
							->suffixAction(
								ActionsAction::make('addAgent')
									->icon('heroicon-m-plus')
									->tooltip('Add New Agent')
									->url('#agent-modal'),
							),
					]),
				Select::make('invoice_id')
					->label('Order for Invoice')
					->searchable()
					->reactive()
					->dehydrated()
					->columnSpan([
						'default' => 8,
						'lg' => 4,
					])
					->options(function() {
						return Invoice::where('parent_invoice_id', null)
							//where invoice_date after 18 June 2025. record di database type DATE 2025-07-18
							// ->whereDate('created_date', '>', '2025-07-19')
							->whereDate('created_at', '>=', Carbon::today())
							->where('order_id', null)
							->whereDoesntHave('order')
							->pluck('invoice_no', 'id');
					})
					->afterStateUpdated(function ($get, $set, $state) {
						if (!$state) {
							$set('order_date', null);
							$set('company_id', null);
							$set('currency_id', null);
							$set('order_amount', null);
							$set('rates', null);
							$set('sell_rates', null);
							$set('total', null);
							$set('status', 'Draft');
							return;
						}
						$invoice = Invoice::find($state);
						//set order data
						// $set('order_date', $invoice->invoice_date);
						$set('company_id', $invoice->client_id);
						$set('currency_id', $invoice->currency_id);
						$set('order_amount', $invoice->invoice_amount);
						$set('rates', $invoice->rates);
						$set('total', $invoice->invoice_amount);
						$set('status', 'Invoiced');
						if ($get('sell_rates') === null) {
							$selRates = $invoice->rates + 100;
							$set('sell_rates', $selRates);
						}
					}),

				DatePicker::make('order_date')
					->columnSpan([
						'default' => 8,
						'lg' => 4,
					])
					->default(today())
					->timezone('Asia/Jakarta')
					->required()
					->native(false),

				Select::make('company_id')
					->label('Select Client')
					->helperText('Note: This will become the client in the invoice')
					->searchable()
					->required()
					->columnSpan([
						'default' => 8,
						'lg' => 4,
					])
					->reactive()
					->options(fn() => Company::where('type', 2)->where('name','like','PT%')->pluck('name', 'id'))
					->suffixAction(
						ActionsAction::make('addCompany')
							->icon('heroicon-m-plus')
							->tooltip('Add New Company')
							->url('/admin/companies/create')
							// ->url('#company-modal')
							->openUrlInNewTab(),
					),

				Select::make('bank_id')
					->label('Select Bank')
					->searchable()
					->required()
					->columnSpan([
						'default' => 8,
						'lg' => 4,
					])
					->helperText('The order amount will be debited from the selected bank account.')
					->reactive()
					->options(fn($get) => CompanyBank::where('company_id', $get('company_id'))
						->pluck('bank_name', 'id'))
					->afterStateUpdated(function (callable $set, $state, callable $get) {
						$balance = CompanyDepositSummary::where('bank_id', $state)->first()->balance ?? 0;
						// Log::info('balance: ' . $balance);
						$set('balance', $balance);
					}),

				Placeholder::make('warning')
					->hiddenLabel()
					->columnSpanFull()
					->reactive()
					->hidden(fn($get) => $get('bank_id') === null)
					->content(function ($get) {
						$balance = number_format($get('balance'), 2, ',', '.');
						return 'Balance available: IDR ' . $balance;
					})
					->extraAttributes(fn($get) => [
						'class' => ($get('balance') <= 0
							? 'text-danger-500'
							: 'text-primary-500'
						) . ' mb-3 mt-3 text-center uppercase border rounded border-gray-200 p-4 font-bold',
					]),
				Fieldset::make('Order')
					->columnSpan([
						'default' => 8,
						'lg' => 4,
					])
					// ->columnStart([
					// 	'default'=>1,
					// 	'lg'=>3,
					// ])
					->schema([
						Select::make('currency_id')
							->searchable()
							->label('Currency')
							->inlineLabel()
							->columnSpanFull()
							->required()
							->placeholder(null)
							->live(onBlur: true)
							->options(
								Currency::get()
									->mapWithKeys(fn($currency) => [
										$currency->id => "{$currency->symbol} - {$currency->name}"
									])
									->toArray()
							),

						TextInput::make('order_amount')
							->numeric()
							->inlineLabel()
							->columnSpanFull()
							->required()
							->live(onBlur: true)
							->extraInputAttributes(['class' => 'text-end'])
							->prefix(fn($get) => $get('currency_id') ? (Currency::find($get('currency_id'))?->symbol ?? '') : '')
							->afterStateUpdated(function (callable $set, $state, callable $get) {
								// $bookingFee = 30;
								$charges = 0;

								// if ($state >= 100000) {
								// 	$bookingFee = 100;
								// } elseif ($state >= 50000) {
								// 	$bookingFee = 50;
								// }

								// if ($state <= 20000) {
								// 	$charges = 85000;
								// } else {
								// 	$charges = 0;
								// }

								// $set('booking_fee', $bookingFee);
								$set('charges', $charges);

								// Hindari null saat get('booking_fee')
								$total = $state + $get('charges');
								$total = $state;
								$set('total', $total);
							}),

						TextInput::make('booking_fee')
							->numeric()
							->inlineLabel()
							->prefix(fn($get) => $get('currency_id') ? (Currency::find($get('currency_id'))?->symbol ?? '') : '')
							->columnSpanFull()
							->live(onBlur: true)
							->extraInputAttributes(['class' => 'text-end'])
							->afterStateUpdated(function (callable $set, $state, callable $get) {
								// $set('total', $state + $get('order_amount'));
							}),

						Placeholder::make('totalView')
							->label('Total Order')
							->reactive()
							->content(function ($get) {
								$currency = $get('currency_id');
								if ($currency) {
									$currency = Currency::find($currency);
								}
								$symbol = $currency?->symbol ?? '';
								$total = number_format($get('total') ?? 0, 2, ',', '.');
								return new HtmlString("<div>{$symbol} {$total}</div>");
							})
							->inlineLabel()
							->extraAttributes(['class' => 'text-end font-bold'])
							->columnSpanFull(),
					]),
				Fieldset::make('Bill')
					->columnSpan([
						'default' => 8,
						'lg' => 4,
					])
					->schema([
						TextInput::make('rates')
							->numeric()
							->label('Bank Rates')
							->inlineLabel()
							->columnSpanFull()
							->prefix('IDR')
							->live(onBlur: true)
							->required()
							->extraInputAttributes(['class' => 'text-end'])
							->afterStateUpdated(function ($get, $set, $state) {
								if ($get('sell_rates') === null) {
									$selRates = $state + 100;
									$set('sell_rates', $selRates);
								}
							}),
						TextInput::make('sell_rates')
							->numeric()
							->label('Sell Rates')
							->inlineLabel()
							->columnSpanFull()
							->prefix('IDR')
							->required()
							->live(onBlur: true)
							->extraInputAttributes(['class' => 'text-end']),
						TextInput::make('charges')
							->numeric()
							->inlineLabel()
							->columnSpanFull()
							->prefix('IDR')
							->live(onBlur: true)
							->extraInputAttributes(['class' => 'text-end']),

						Placeholder::make('totalBill')
							->label('Total Bill')
							->reactive()
							->content(function ($get) {
								$orderAfterRate = (($get('total') ?? 0) * floatval($get('sell_rates') ?? 0));

								$totalIdr = number_format($orderAfterRate + floatval($get('charges') ?? 0), 2, ',', '.');
								return new HtmlString("<div>IDR {$totalIdr}</div>");
							})
							->inlineLabel()
							->extraAttributes(['class' => 'text-end font-bold'])
							->columnSpanFull(),
					]),
			])
			->columns(8)
			->statePath('data');
	}

	public function create(): void
	{
		$data = $this->form->getState();
		try {
			$invoice = Invoice::find($data['invoice_id']);
			Invoice::$skipBootedEvent = filled($data['invoice_id'] ?? null);
			$order = Order::create($data);

			// Update invoice utama
			if ($data['invoice_id']) {
				$invoice = Invoice::find($data['invoice_id']);

				$invoice->order_id = $order->id;
				$invoice->order_amount = $order->order_amount;
				$invoice->save();

				// Rekursif update child invoice
				$invoice->assignOrderToDescendants($order->id);
			}
			Invoice::$skipBootedEvent = false;
			Notification::make()
				->title('Order Created Successfully')
				->success()
				->send();

			// Reset form
			$this->form->fill();

			// Dispatch event to refresh other widgets
			$this->dispatch('order-created');
		} catch (\Exception $e) {
			Notification::make()
				->title('Error Creating Order')
				->body($e->getMessage())
				->danger()
				->send();
		}
	}
}
