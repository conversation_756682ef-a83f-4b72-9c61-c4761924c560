<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\CompanyTransactionResource\Pages;
use App\Filament\Admin\Resources\CompanyTransactionResource\RelationManagers;
use App\Models\CompanyTransaction;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class CompanyTransactionResource extends Resource
{
    protected static ?string $model = CompanyTransaction::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                //
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\DepositsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCompanyTransactions::route('/'),
            'create' => Pages\CreateCompanyTransaction::route('/create'),
            'view' => Pages\ViewCompanyTransaction::route('/{record}'),
            'edit' => Pages\EditCompanyTransaction::route('/{record}/edit'),
        ];
    }
}
