<?php

namespace App\Filament\Admin\Resources\CompanyTransactionResource\Widgets;

use App\Models\Company;
use App\Models\CompanyDeposito;
use App\Models\CompanyTransaction;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class Widget extends BaseWidget
{
	protected int | string | array $columnSpan = 'full';

	protected static string $view = 'filament.admin.resources.deposit-resource.pages.company-transaction-details';


	public CompanyTransaction $record;

	public function mount(CompanyTransaction $record): void
	{
		$this->record = $record;
	}

	public function getViewData(): array
	{
		$companyId = $this->record->id;
		$now = now('Asia/Jakarta');

		// Awal bulan ini & bulan lalu
		$startOfThisMonth = $now->copy()->startOfMonth()->toDateString(); // YYYY-MM-01
		$startOfLastMonth = $now->copy()->subMonth()->startOfMonth()->toDateString(); // awal bulan lalu
		$endOfLastMonth = $now->copy()->subMonth()->endOfMonth()->toDateString(); // akhir bulan lalu

		// Ambil semua data untuk log/debug
		$data = CompanyDeposito::where('company_id', $companyId)->get();

		// Jumlahkan amount untuk trx_type tertentu sampai akhir bulan lalu
		$inLastMonth = $data
			->where('trx_type', 'in')
			->whereBetween('trx_date', [$startOfLastMonth, $endOfLastMonth])
			->sum('amount');

		$outLastMonth = $data
			->where('trx_type', 'out')
			->whereBetween('trx_date', [$startOfLastMonth, $endOfLastMonth])
			->sum('amount');

		$orderLastMonth = $data
			->where('trx_type', 'order')
			->whereBetween('trx_date', [$startOfLastMonth, $endOfLastMonth])
			->sum('amount');

		$balanceLastMonth = $inLastMonth - ($outLastMonth + $orderLastMonth);

		$inThisMonth = $data
			->where('trx_type', 'in')
			->where('trx_date', '>=', $startOfThisMonth)
			->sum('amount');

		$outThisMonth = CompanyDeposito::where('company_id', $companyId)
			->where('trx_type', 'out')
			->where('trx_date', '>=', $startOfThisMonth)
			->sum('amount');

		$orderThisMonth = CompanyDeposito::where('company_id', $companyId)
			->where('trx_type', 'order')
			->where('trx_date', '>=', $startOfThisMonth)
			->sum('amount');

		$totalBalance = $balanceLastMonth + $inThisMonth - ($outThisMonth + $orderThisMonth);

		return [
			'balanceLastMonth' => $balanceLastMonth,
			'cashInCurrentMonth' => $inThisMonth,
			'cashOutCurrentMonth' => $outThisMonth,
			'orderCurrentMonth' => $orderThisMonth,
			'totalBalance' => $totalBalance,
		];
	}
}
