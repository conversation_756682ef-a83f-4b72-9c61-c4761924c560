<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['invoice']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['invoice']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
    // Build chain data
    $chainData = [];
    $current = $invoice;

    // Go to root (main invoice)
    while ($current->parent_invoice_id) {
        $current = $current->parentInvoice;
    }

    // Build chain from root
    $chainData[] = $current;
    while ($current->childInvoice()->exists()) {
        $current = $current->childInvoice()->first();
        $chainData[] = $current;
    }

    $currentInvoiceId = $invoice->id;
    $totalChain = count($chainData);
    $rootInvoice = $chainData[0];
	if($invoice->order){
		$bankOrder = $rootInvoice->order?->bank_id;

		$bankDeposit = optional($rootInvoice->order?->deposits)->pluck('bank_id') ?? collect();
		$companyBanks = optional($rootInvoice->order?->company)->banks?->pluck('id') ?? collect();

		$isBankOrderValid = $companyBanks->contains($bankOrder);
		$isBankDepositValid = $bankDeposit->intersect($companyBanks)->isNotEmpty();

		if ($isBankOrderValid && $isBankDepositValid) {
			$desc = 'Successfully Debited';
			$color = 'success';
		} else {
			$desc = 'Unrecorded transaction or invalid bank';
			$color = 'danger';
		}

		// Log::info('Bank Order: ' . $bankOrder);

		$rootCompanyId = $current->order?->company_id;
		$rootClientId = $current->order->invoices()->first()->id;
	}
?>

<div class="bg-gray-50 dark:bg-transparent p-4 rounded-lg border dark:border-0">
    <div class="flex items-center justify-between mb-3">
        <h4 class="text-sm font-medium text-gray-900"> </h4>
        <span class="text-xs text-gray-500"><?php echo e($totalChain); ?> <?php echo e($totalChain > 1 ? 'invoices' : 'invoice'); ?>

            chainings</span>
    </div>

    <div class="flex items-center space-x-2 overflow-x-auto pb-2">
        <!--[if BLOCK]><![endif]--><?php if($invoice->order): ?>
            <div class="flex-shrink-0 relative w-48">
                <div class="flex flex-col items-center w-full">
                    <div
                        class="px-2 py-3 rounded-lg border-2 text-center w-48 flex flex-col justify-between relative transition-all duration-200">
                        <div class="flex-1 flex items-start justify-start mb-3">
                            <div class="flex justify-start items-center gap-2">
                                <div class="text-xs font-semibold leading-tight text-start overflow-hidden"
                                    title="<?php echo e($invoice->order->order_no ?? 'Unknown'); ?>">
                                    <div
                                        style="display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; overflow: hidden; word-break: break-word; line-height: 1.2;">
                                        <?php echo e($invoice->order->order_no ?? 'Unknown'); ?>

                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-xs">#<?php echo e($invoice->order->id); ?></span>
                            <?php if (isset($component)) { $__componentOriginal986dce9114ddce94a270ab00ce6c273d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal986dce9114ddce94a270ab00ce6c273d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.badge','data' => ['size' => 'sm','color' => $invoice->order->deposits()->exists() ? 'success' : 'danger']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::badge'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['size' => 'sm','color' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($invoice->order->deposits()->exists() ? 'success' : 'danger')]); ?>
                                Parent Order
                             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal986dce9114ddce94a270ab00ce6c273d)): ?>
<?php $attributes = $__attributesOriginal986dce9114ddce94a270ab00ce6c273d; ?>
<?php unset($__attributesOriginal986dce9114ddce94a270ab00ce6c273d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal986dce9114ddce94a270ab00ce6c273d)): ?>
<?php $component = $__componentOriginal986dce9114ddce94a270ab00ce6c273d; ?>
<?php unset($__componentOriginal986dce9114ddce94a270ab00ce6c273d); ?>
<?php endif; ?>

                        </div>
                    </div>

                    
                    <div class="mt-2 text-xs text-gray-500 dark:text-gray-300 text-center w-48 h-4 flex items-center justify-end"
                        title="<?php echo e($invoice->order->order_no); ?>">
                        <div class="truncate px-2 text-<?php echo e($color); ?>-500" style="max-width: 100%;">
                            → <?php echo e($desc); ?>

                        </div>
                    </div>
                </div>
            </div>

            <div class="flex-shrink-0 text-gray-400 flex items-center px-2">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6">
                    </path>
                </svg>
            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $chainData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $chainInvoice): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            
            <div class="flex-shrink-0 relative w-48">
                <div class="flex flex-col items-center w-full">
                    
                    <?php
                        $isActive = $chainInvoice->id === $currentInvoiceId;
                        $statusColor = match ($chainInvoice->status) {
                            'Draft' => 'danger',
                            'Issued' => 'info',
                            'Closed' => 'success',
                            default => 'danger',
                        };
                    ?>
                    <div
                        class="px-2 py-3 rounded-lg border-2 text-center w-48 flex flex-col justify-between relative transition-all duration-200
							<?php echo e($isActive
           ? 'bg-white dark:bg-transparent border-orange-600 text-orange-900 dark:text-orange-100 shadow-md'
           : 'bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-700 text-gray-700 dark:text-gray-200 hover:border-gray-400 dark:hover:border-gray-500'); ?>">
                        <div class="flex-1 flex items-start justify-start mb-3">
                            <div class="flex justify-start items-center gap-2">
                                <div class="w-5 h-5 rounded-full border-2 flex items-center justify-center font-bold shrink-0"
                                    style="font-size: 0.6rem">
                                    <?php echo e($index === 0 ? 'M' : 'L' . ($index + 1)); ?>

                                </div>
                                <div class="text-xs font-semibold leading-tight text-start overflow-hidden"
                                    title="<?php echo e($chainInvoice->company->name ?? 'Unknown'); ?>">
                                    <div
                                        style="display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; overflow: hidden; word-break: break-word; line-height: 1.2;">
                                        <?php echo e($chainInvoice->company->name ?? 'Unknown'); ?>

                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-xs">#<?php echo e($chainInvoice->id); ?></span>
                            <?php if (isset($component)) { $__componentOriginal986dce9114ddce94a270ab00ce6c273d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal986dce9114ddce94a270ab00ce6c273d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.badge','data' => ['size' => 'sm','color' => ''.e($statusColor).'']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::badge'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['size' => 'sm','color' => ''.e($statusColor).'']); ?>
                                <?php echo e($chainInvoice->status ? $chainInvoice->status : 'no status'); ?>

                             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal986dce9114ddce94a270ab00ce6c273d)): ?>
<?php $attributes = $__attributesOriginal986dce9114ddce94a270ab00ce6c273d; ?>
<?php unset($__attributesOriginal986dce9114ddce94a270ab00ce6c273d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal986dce9114ddce94a270ab00ce6c273d)): ?>
<?php $component = $__componentOriginal986dce9114ddce94a270ab00ce6c273d; ?>
<?php unset($__componentOriginal986dce9114ddce94a270ab00ce6c273d); ?>
<?php endif; ?>
                        </div>
                    </div>

                    
                    <!--[if BLOCK]><![endif]--><?php if($chainInvoice->client): ?>
                        <div class="mt-2 text-xs text-gray-500 dark:text-gray-300 text-center w-48 h-4 flex items-center justify-end"
                            title="<?php echo e($chainInvoice->client->name); ?>">
                            <div class="truncate px-2" style="max-width: 100%;">
                                <?php
                                    $clientName = $chainInvoice->client->name;
                                    $maxLength = 25; // Limit to 25 characters
                                    if (strlen($clientName) > $maxLength) {
                                        $clientName = substr($clientName, 0, $maxLength - 3) . '...';
                                    }
                                ?>
                                → <?php echo e($clientName); ?>

                            </div>
                        </div>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </div>
            </div>

            
            <!--[if BLOCK]><![endif]--><?php if($index < count($chainData) - 1): ?>
                <div class="flex-shrink-0 text-gray-400 flex items-center px-2">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                    </svg>
                </div>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
        <?php
            $lastClient = collect($chainData)->last()?->client;
        ?>

        <!--[if BLOCK]><![endif]--><?php if($lastClient): ?>
            

            <div class="flex-shrink-0 text-gray-400 flex items-center px-2">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6">
                    </path>
                </svg>
            </div>
            <div class="flex-shrink-0 relative w-40">
                <div class="flex flex-col items-center w-full">
                    <div
                        class="px-2 py-3 rounded-lg border-2 text-center w-40 flex flex-col justify-between relative bg-gray-50 dark:bg-transparent border-gray-300 dark:border-gray-700 ">
                        <div class="flex justify-start items-center gap-2">
                            <?php if (isset($component)) { $__componentOriginalbfc641e0710ce04e5fe02876ffc6f950 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalbfc641e0710ce04e5fe02876ffc6f950 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.icon','data' => ['alias' => 'panels::topbar.global-search.field','icon' => 'heroicon-m-check-circle','wire:target' => 'search','class' => 'h-4 w-4 text-success-500  shrink-0']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['alias' => 'panels::topbar.global-search.field','icon' => 'heroicon-m-check-circle','wire:target' => 'search','class' => 'h-4 w-4 text-success-500  shrink-0']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalbfc641e0710ce04e5fe02876ffc6f950)): ?>
<?php $attributes = $__attributesOriginalbfc641e0710ce04e5fe02876ffc6f950; ?>
<?php unset($__attributesOriginalbfc641e0710ce04e5fe02876ffc6f950); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalbfc641e0710ce04e5fe02876ffc6f950)): ?>
<?php $component = $__componentOriginalbfc641e0710ce04e5fe02876ffc6f950; ?>
<?php unset($__componentOriginalbfc641e0710ce04e5fe02876ffc6f950); ?>
<?php endif; ?>
                            <div class="text-sm font-semibold leading-tight">
                                <?php echo e(Str::limit($lastClient->name, 30)); ?>

                            </div>
                        </div>
                    </div>
                    <div
                        class="mt-2 text-xs text-gray-500 dark:text-gray-300 text-center w-40 h-4 flex items-center justify-center">
                        <div class="truncate px-2">
                            End of Chaining
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    </div>

    
    <div class="mt-3 pt-3 border-t border-gray-200">
        <div class="flex justify-between text-xs text-gray-500 dark:text-gray-300">
            <span>
                <strong>Your are here:</strong>
                <?php
                    $currentIndex = array_search($currentInvoiceId, array_column($chainData, 'id'));
                ?>
                <?php echo e($currentIndex === 0 ? 'Main Invoice' : 'Chain Level ' . ($currentIndex + 1)); ?>

                (<?php echo e($currentIndex + 1); ?> of <?php echo e($totalChain); ?> invoice chainings)
            </span>
            <span>
                <strong>Total Value:</strong>
                <?php echo e($chainData[0]->currency->symbol ?? '$'); ?>

                <?php echo e(number_format($chainData[0]->invoice_amount ?? 0, 2)); ?>

            </span>
        </div>
    </div>

    
    <div class="mt-3 pt-3 border-t border-gray-200">
        <div class="flex space-x-2">
            <!--[if BLOCK]><![endif]--><?php if($invoice->parent_invoice_id): ?>
                <?php if (isset($component)) { $__componentOriginal6330f08526bbb3ce2a0da37da512a11f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6330f08526bbb3ce2a0da37da512a11f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.button.index','data' => ['color' => 'info','size' => 'xs','icon' => 'heroicon-o-arrow-left-circle','href' => ''.e(route('filament.admin.resources.invoices.view', $invoice->parent_invoice_id)).'','tag' => 'a']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['color' => 'info','size' => 'xs','icon' => 'heroicon-o-arrow-left-circle','href' => ''.e(route('filament.admin.resources.invoices.view', $invoice->parent_invoice_id)).'','tag' => 'a']); ?>
                    View Parent
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6330f08526bbb3ce2a0da37da512a11f)): ?>
<?php $attributes = $__attributesOriginal6330f08526bbb3ce2a0da37da512a11f; ?>
<?php unset($__attributesOriginal6330f08526bbb3ce2a0da37da512a11f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6330f08526bbb3ce2a0da37da512a11f)): ?>
<?php $component = $__componentOriginal6330f08526bbb3ce2a0da37da512a11f; ?>
<?php unset($__componentOriginal6330f08526bbb3ce2a0da37da512a11f); ?>
<?php endif; ?>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

            <!--[if BLOCK]><![endif]--><?php if($invoice->childInvoice()->exists()): ?>
                <?php if (isset($component)) { $__componentOriginal6330f08526bbb3ce2a0da37da512a11f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6330f08526bbb3ce2a0da37da512a11f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.button.index','data' => ['color' => 'info','size' => 'xs','icon' => 'heroicon-o-arrow-right-circle','iconPosition' => 'after','href' => ''.e(route('filament.admin.resources.invoices.view', $invoice->childInvoice()->first()->id)).'','tag' => 'a']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['color' => 'info','size' => 'xs','icon' => 'heroicon-o-arrow-right-circle','icon-position' => 'after','href' => ''.e(route('filament.admin.resources.invoices.view', $invoice->childInvoice()->first()->id)).'','tag' => 'a']); ?>
                    View Child
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6330f08526bbb3ce2a0da37da512a11f)): ?>
<?php $attributes = $__attributesOriginal6330f08526bbb3ce2a0da37da512a11f; ?>
<?php unset($__attributesOriginal6330f08526bbb3ce2a0da37da512a11f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6330f08526bbb3ce2a0da37da512a11f)): ?>
<?php $component = $__componentOriginal6330f08526bbb3ce2a0da37da512a11f; ?>
<?php unset($__componentOriginal6330f08526bbb3ce2a0da37da512a11f); ?>
<?php endif; ?>
                
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

            <!--[if BLOCK]><![endif]--><?php if($totalChain === 1): ?>
                <span class="inline-flex items-center px-2 py-1 text-xs bg-blue-100 text-blue-600 rounded">
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                    Single Invoice (No Chain)
                </span>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        </div>
    </div>
</div>
<?php /**PATH D:\sites\starterkit\web_starter\resources\views/components/invoice-chain-visualization.blade.php ENDPATH**/ ?>