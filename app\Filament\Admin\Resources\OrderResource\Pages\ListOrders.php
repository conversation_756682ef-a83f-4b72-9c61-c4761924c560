<?php

namespace App\Filament\Admin\Resources\OrderResource\Pages;

use App\Filament\Admin\Resources\OrderResource;
use App\Models\Company;
use App\Models\CompanyBank;
use App\Models\Invoice;
use App\Models\MasterInvoiceDetail;
use App\Models\Order;
use Carbon\Carbon;
use Filament\Actions;
use Filament\Forms\Components\{DatePicker, Select};
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ListRecords;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\{SelectColumn, TextColumn, TextInputColumn};
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Table;
use Illuminate\Contracts\Pagination\Paginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ListOrders extends ListRecords
{
    protected static string $resource = OrderResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->label('New Order')
                ->modalHeading('New Order')
                ->icon('heroicon-o-plus')
                ->modalWidth('3xl'),
        ];
    }

    protected static ?string $title = 'Orders';

    public function getHeading(): string
	{
        // if(!Auth::user()->hasRole('Staff Order'))
        // {
        //     return 'Orders tobe Invoiced';
        // }
        return 'List of Orders';
	}

    protected function paginateTableQuery(Builder $query): Paginator
    {
        return $query->simplePaginate(($this->getTableRecordsPerPage() === 'all') ? $query->count() : $this->getTableRecordsPerPage());
    }

    public function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(function (Builder $query) {
                if (Auth::user()->hasAnyRole(['Staff Invoice'])) {
                    return $query->whereIn('status', ['Forwarded', 'Invoiced']);
                }
            })
            ->columns([
				TextColumn::make('order_no')
					->label('Order No')
					->searchable()
					->sortable(),
				TextColumn::make('company.name')
					->label('Company')
					->searchable()
					->sortable(),
                TextColumn::make('order_date')
                    ->date()
                    ->sortable(),
                TextColumn::make('order_amount')
                    ->formatStateUsing(fn ($record, $state) => $record->currency->symbol . ' ' . number_format($state, 2, ',', '.'))
                    ->alignEnd()
                    ->sortable(),
                TextColumn::make('booking_fee')
                    ->formatStateUsing(fn ($record, $state) => $record->currency->symbol . ' ' . number_format($state, 2, ',', '.'))
                    ->alignEnd()
                    ->sortable(),
                TextColumn::make('total')
                    ->formatStateUsing(fn ($record, $state) => $record->currency->symbol . ' ' . number_format($state, 2, ',', '.'))
                    ->alignEnd()
                    ->sortable(),
                TextColumn::make('status')
                    ->searchable()
					->badge()
					->color(fn ($record) => match ($record->status) {
						'Draft' => 'danger',
						'Forwarded' => 'warning',
						'Invoiced' => 'success',
					})
					->formatStateUsing(function ($state) {
						if(Auth::user()->hasRole('Staff Invoice'))
						{
							return match ($state) {
								'Forwarded' => 'New',
								'Invoiced' => 'Invoiced',
							};
						}
						return match ($state) {
							'Draft' => 'Draft',
							'Forwarded' => 'New',
							'Invoiced' => 'Invoiced',
						};
					}),
                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Filter::make('created_from')
                    ->form([
                        DatePicker::make('date_from')->label('Created From'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            $data['date_from'],
                            fn (Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                        );
                    }),
                Filter::make('created_until')
                    ->form([
                        DatePicker::make('date_until')->label('Created Until'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            $data['date_until'],
                            fn (Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                        );
                    }),
                SelectFilter::make('status')
                    ->options(function () {
                        if(Auth::user()->hasRole('Staff Invoice'))
                        {
                            return [
                                'Forwarded' => 'New',
                                'Invoiced' => 'Invoiced',
                            ];
                        }
                        if(Auth::user()->hasRole('Staff Order'))
                        {
                            return [
                                'Draft' => 'Draft',
                                'Forwarded' => 'Forwarded',
                            ];
                        }
                        return [];
                    })
                    ->default(function () {
                        if(Auth::user()->hasRole('Staff Invoice'))
                        {
                            return 'Forwarded';
                        }
                        if(Auth::user()->hasRole('Staff Order'))
                        {
                            return 'Draft';
                        }
                        return '';
                    }),
            ], layout: FiltersLayout::AboveContentCollapsible)
            ->filtersFormColumns(3)
			->hiddenFilterIndicators()
            ->actions([
				ActionGroup::make([
					ViewAction::make()
						->tooltip('View')
						->modalHeading(fn ($record) => 'Order '.' '. $record->company->name),
					EditAction::make()
						->tooltip('Edit')
						// ->hidden(fn (Order $record) => $record->status !== 'Draft')
						->visible(fn () => Auth::user()->hasAnyRole(['Staff Order', 'Admin', 'Super Admin'])),
					DeleteAction::make()
						->tooltip('Delete')
						->visible(fn () => Auth::user()->hasAnyRole(['Staff Order', 'Admin', 'Super Admin']))
						//logika hidden, jika status sudah bukan draft,
						// ->hidden(fn (Order $record) => $record->status !== 'Draft')
						->requiresConfirmation()
						->modalHeading('Delete Order')
						->modalDescription('Are you sure you want to delete this order?')
						->modalSubmitActionLabel('Yes, delete it'),
				]),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }
}
